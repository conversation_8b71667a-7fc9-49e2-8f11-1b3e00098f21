/*
 * SPDX-License-Identifier: Apache-2.0
 *
 * The OpenSearch Contributors require contributions made to
 * this file be licensed under the Apache-2.0 license or a
 * compatible open source license.
 *
 * Modifications Copyright OpenSearch Contributors. See
 * GitHub history for details.
 */

spotless {
  java {
    target '**/*.java'

    removeUnusedImports()
    importOrder(
        'de.thetaphi',
        'com.carrotsearch',
        'com.fasterxml',
        'com.avast',
        'com.sun',
        'com.maxmind|com.github|com.networknt|groovy|nebula',
        'org.antlr',
        'software.amazon',
        'com.azure|com.microsoft|com.ibm|com.google|joptsimple|org.apache|org.bouncycastle|org.codehaus|org.opensearch|org.objectweb|org.joda|org.hamcrest|org.openjdk|org.gradle|org.junit',
        'javax',
        'java',
        '',
        '\\#java|\\#org.opensearch|\\#org.hamcrest|\\#'
    )

    eclipse().configFile rootProject.file('gradle/formatterConfig.xml')
    trimTrailingWhitespace()
    endWithNewline()

    custom 'Refuse wildcard imports', {
          // Wildcard imports can't be resolved; fail the build
          if (it =~ /\s+import .*\*;/) {
              throw new AssertionError("Do not use wildcard imports.  'spotlessApply' cannot resolve this issue.")
          }
    }

    // See DEVELOPER_GUIDE.md for details of when to enable this.
    if (System.getProperty('spotless.paddedcell') != null) {
      paddedCell()
    }
  }
  format 'misc', {
      target '*.md', '*.gradle', '**/*.json', '**/*.yaml', '**/*.yml', '**/*.svg'

      targetExclude '**/simple-bulk11.json', '**/simple-msearch5.json'

      trimTrailingWhitespace()
      endWithNewline()
  }
}

precommit.dependsOn 'spotlessJavaCheck'
