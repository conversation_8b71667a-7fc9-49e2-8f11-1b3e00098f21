/*
 * SPDX-License-Identifier: Apache-2.0
 *
 * The OpenSearch Contributors require contributions made to
 * this file be licensed under the Apache-2.0 license or a
 * compatible open source license.
 *
 * Modifications Copyright OpenSearch Contributors. See
 * GitHub history for details.
 */

/*
 * Licensed to Elasticsearch under one or more contributor
 * license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright
 * ownership. Elasticsearch licenses this file to you under
 * the Apache License, Version 2.0 (the "License"); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

buildscript {
  ext {
    opensearch_group = "org.opensearch"
    opensearch_version = System.getProperty("opensearch.version", "3.1.0-SNAPSHOT")
    isSnapshot = "true" == System.getProperty("build.snapshot", "true")
    buildVersionQualifier = System.getProperty("build.version_qualifier", "")
  }

  repositories {
    mavenLocal()
    mavenCentral()
    maven { url "https://aws.oss.sonatype.org/content/repositories/snapshots" }
    maven { url "https://plugins.gradle.org/m2/" }
  }

  dependencies {
    classpath "${opensearch_group}.gradle:build-tools:${opensearch_version}"
    classpath "org.jacoco:org.jacoco.agent:0.8.5"
  }
}

plugins {
  id 'java'
  id 'com.diffplug.spotless' version '6.25.0'
}

apply plugin: 'opensearch.opensearchplugin'
apply plugin: 'opensearch.internal-cluster-test'
apply plugin: 'opensearch.pluginzip'
apply plugin: 'opensearch.rest-test'
apply plugin: 'opensearch.java-agent'
apply from: 'gradle/formatting.gradle'

repositories {
  mavenLocal()
  mavenCentral()
  maven { url "https://plugins.gradle.org/m2/" }
  maven { url "https://aws.oss.sonatype.org/content/repositories/snapshots" }
}

allprojects {
  group 'org.opensearch'
  version = opensearch_version.tokenize('-')[0] + '.0'
  if (buildVersionQualifier) {
    version += "-${buildVersionQualifier}"
  }
  if (isSnapshot) {
    version += "-SNAPSHOT"
  }

  java {
      targetCompatibility = JavaVersion.VERSION_21
      sourceCompatibility = JavaVersion.VERSION_21
  }
}

opensearchplugin {
  name 'opensearch-custom-codecs'
  description 'OpenSearch plugin that implements custom compression codecs'
  classname 'org.opensearch.index.codec.customcodecs.CustomCodecPlugin'
  licenseFile rootProject.file('LICENSE')
  noticeFile rootProject.file('NOTICE')
}

dependencies {
  api "com.github.luben:zstd-jni:1.5.6-1"
  api "com.intel.qat:qat-java:2.3.2"
}

allprojects {
    // Default to the apache license
    project.ext.licenseName = 'The Apache Software License, Version 2.0'
    project.ext.licenseUrl = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
    publishing {
        repositories {
            maven {
                name = 'staging'
                url = "${rootProject.buildDir}/local-staging-repo"
            }
        }
        publications {
            // add license information to generated poms
            pluginZip(MavenPublication) { publication ->
                pom {
                    name = "opensearch-custom-codecs"
                    description = "OpenSearch plugin that implements custom compression codecs"
                }
                pom.withXml { XmlProvider xml ->
                    Node node = xml.asNode()
                    node.appendNode('inceptionYear', '2021')

                    Node license = node.appendNode('licenses').appendNode('license')
                    license.appendNode('name', project.licenseName)
                    license.appendNode('url', project.licenseUrl)

                    Node developer = node.appendNode('developers').appendNode('developer')
                    developer.appendNode('name', 'OpenSearch')
                    developer.appendNode('url', 'https://github.com/opensearch-project/custom-codecs')
                }
            }
        }
    }
}

publishing {
    publications {
        pluginZip(MavenPublication) { publication ->
            pom {
                name = "opensearch-custom-codecs"
                description = "OpenSearch plugin that implements custom compression codecs"
                groupId = "org.opensearch.plugin"
            }
        }
    }
    repositories {
        maven {
            name = "Snapshots"
            url = "https://aws.oss.sonatype.org/content/repositories/snapshots"
            credentials {
                username "$System.env.SONATYPE_USERNAME"
                password "$System.env.SONATYPE_PASSWORD"
            }
        }
    }
}

// ignore missing javadocs
tasks.withType(Javadoc).configureEach { Javadoc javadoc ->
  // the -quiet here is because of a bug in gradle, in that adding a string option
  // by itself is not added to the options. By adding quiet, both this option and
  // the "value" -quiet is added, separated by a space. This is ok since the javadoc
  // command already adds -quiet, so we are just duplicating it
  // see https://discuss.gradle.org/t/add-custom-javadoc-option-that-does-not-take-an-argument/5959
  javadoc.options.encoding = 'UTF8'
  javadoc.options.addStringOption('Xdoclint:all,-missing', '-quiet')
  boolean failOnJavadocWarning = project.ext.has('failOnJavadocWarning') ? project.ext.get('failOnJavadocWarning') : true
  if (failOnJavadocWarning) {
    javadoc.options.addStringOption('Xwerror', '-quiet')
  }
  javadoc.options.tags = ["opensearch.internal", "opensearch.api", "opensearch.experimental"]
  javadoc.options.addStringOption("-release", java.targetCompatibility.majorVersion)
}

loggerUsageCheck.enabled = false
validateNebulaPom.enabled = false

sourceSets {
  integTest {
    java {
        srcDirs file("src/integrationTest/java")
    }
    compileClasspath += sourceSets["main"].output + configurations["testRuntimeClasspath"]
    runtimeClasspath += output + compileClasspath
  }
}

tasks.named("testingConventions").configure {
    naming.clear()
    naming {
        Tests {
            baseClass "org.apache.lucene.tests.util.LuceneTestCase"
        }
        IT {
            baseClass "org.opensearch.test.OpenSearchIntegTestCase"
            baseClass "org.opensearch.test.OpenSearchSingleNodeTestCase"
        }
    }
}

integTest {
    description = "Run tests against a cluster"
    testClassesDirs = sourceSets.integTest.output.classesDirs
    classpath = sourceSets.integTest.runtimeClasspath

    dependsOn "bundlePlugin"
    systemProperty 'tests.security.manager', 'true'

    systemProperty "https", System.getProperty("https")
    systemProperty "user", System.getProperty("user")
    systemProperty "password", System.getProperty("password")
}

testClusters.integTest {
    testDistribution = "ARCHIVE"
    plugin(project.tasks.bundlePlugin.archiveFile)
}

tasks.withType(PublishToMavenRepository) {
    def predicate = provider {
        publication.name == "pluginZip"
    }
    onlyIf("Publishing only ZIP distributions") {
        predicate.get()
    }
}

task updateVersion {
    onlyIf { System.getProperty('newVersion') }
    doLast {
        ext.newVersion = System.getProperty('newVersion')
        println "Setting version to ${newVersion}."
        // String tokenization to support -SNAPSHOT
        ant.replaceregexp(file:'build.gradle', match: '"opensearch.version", "\\d.*"', replace: '"opensearch.version", "' + newVersion.tokenize('-')[0] + '-SNAPSHOT"', flags:'g', byline:true)
    }
}
