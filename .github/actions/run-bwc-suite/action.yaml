name: 'Runs the backward compatiblity test suite'
description: 'Tests backwards compability between a previous and next version of this plugin'

inputs:
  plugin-previous-branch:
    description: 'The branch of the plugin that should be built for the previous version, e.g "2.2", "1.x"'
    required: true

  plugin-next-branch:
    description: 'The branch of the plugin that should be built for the next version, e.g "2.3", "main"'
    required: true

  report-artifact-name:
    description: 'The name of the artifacts for this run, e.g. "BWC-2.1-to-2.4-results"'
    required: true

  username:
    description: 'Username to use for cluster health check in testClusters'
    required: true

  password:
    description: 'Password to use for cluster health check in testClusters'
    required: true

runs:
  using: "composite"
  steps:

    - id: build-previous
      uses: ./.github/actions/create-bwc-build
      with:
        plugin-branch: ${{ inputs.plugin-previous-branch }}

    - id: build-next
      uses: ./.github/actions/create-bwc-build
      with:
        plugin-branch: ${{ inputs.plugin-next-branch }}

    - name: Run BWC tests
      uses: gradle/gradle-build-action@v3
      with:
        cache-disabled: true
        arguments: |
          -p bwc-test
          bwcTestSuite
          -Dtests.security.manager=true
          -Dtests.opensearch.username=${{ inputs.username }}
          -Dtests.opensearch.password=${{ inputs.password }}
          -Dbwc.version.previous=${{ steps.build-previous.outputs.built-version }}
          -Dbwc.version.next=${{ steps.build-next.outputs.built-version }} -i

    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: ${{ inputs.report-artifact-name }}
        path: |
          ./bwc-test/build/reports/
