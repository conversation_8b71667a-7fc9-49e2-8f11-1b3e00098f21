name: <PERSON><PERSON><PERSON> Check
on: [pull_request]

jobs:
  Get-CI-Image-Tag:
    uses: opensearch-project/opensearch-build/.github/workflows/get-ci-image-tag.yml@main
    with:
      product: opensearch

  precommit-linux:
    needs: Get-CI-Image-Tag
    strategy:
      matrix:
        java: [ 21 ]
    if: github.repository == 'opensearch-project/custom-codecs'
    runs-on: ubuntu-latest
    container:
      # using the same image which is used by opensearch-build team to build the OpenSearch Distribution
      # this image tag is subject to change as more dependencies and updates will arrive over time
      image: ${{ needs.Get-CI-Image-Tag.outputs.ci-image-version-linux }}
      options: ${{ needs.Get-CI-Image-Tag.outputs.ci-image-start-options }}
    steps:
      - name: Run start commands
        run: ${{ needs.Get-CI-Image-Tag.outputs.ci-image-start-command }}
      - uses: actions/checkout@v4
      - name: Run Gradle (check)
        run: |
          # https://github.com/opensearch-project/opensearch-build/issues/4191
          chown -R ci-runner:ci-runner `pwd`
          su ci-runner -c "source /etc/profile.d/java_home.sh && ./gradlew test check -Dorg.gradle.java.home=/opt/java/openjdk-${{ matrix.java }}"
      - name: Run Gradle (assemble)
        run: |
          # https://github.com/opensearch-project/opensearch-build/issues/4191
          chown -R ci-runner:ci-runner `pwd`
          su ci-runner -c "source /etc/profile.d/java_home.sh && ./gradlew assemble -Dorg.gradle.java.home=/opt/java/openjdk-${{ matrix.java }}"

  precommit-windows-macos:
    if: github.repository == 'opensearch-project/custom-codecs'
    strategy:
      matrix:
        java: [ 21 ]
        os: [windows-latest, macos-13]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK ${{ matrix.java }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ matrix.java }}
          distribution: temurin
          cache: gradle
      - name: Run Gradle (check)
        run: |
          ./gradlew test check
      - name: Run Gradle (assemble)
        run: |
          ./gradlew assemble
