name: Apply 'untriaged' label during issue lifecycle

on:
  issues:
    types: [opened, reopened, transferred]

jobs:
  apply-label:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['untriaged']
            })
