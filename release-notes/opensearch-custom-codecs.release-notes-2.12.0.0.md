## 2024-02-08 Version 2.12.0.0

Compatible with OpenSearch 2.12.0

### Features

* Accessors for custom compression modes ([#90](https://github.com/opensearch-project/custom-codecs/pull/90))
* Lucene 9.9.1 Upgrade ([#99](https://github.com/opensearch-project/custom-codecs/pull/99))


### Bug fixes

* GA updates and minor BWC test cleanups ([#105](https://github.com/opensearch-project/custom-codecs/pull/105))

### Infrastructure

* Onboard jenkins prod docker images to github actions ([#77](https://github.com/opensearch-project/custom-codecs/pull/77))
* Switch to ci-runner user for the checks ([#82](https://github.com/opensearch-project/custom-codecs/pull/82))
* Add updateVersion gradle task from plugin template ([#87](https://github.com/opensearch-project/custom-codecs/pull/87))

### Documentation

* Version 2.12 Release Notes ([#110](https://github.com/opensearch-project/custom-codecs/pull/110))