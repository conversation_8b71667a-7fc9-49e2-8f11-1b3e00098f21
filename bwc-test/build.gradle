/*
 * SPDX-License-Identifier: Apache-2.0
 *
 * The OpenSearch Contributors require contributions made to
 * this file be licensed under the Apache-2.0 license or a
 * compatible open source license.
 *
 * Modifications Copyright OpenSearch Contributors. See
 * GitHub history for details.
 */

/*
 * Licensed to Elasticsearch under one or more contributor
 * license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright
 * ownership. Elasticsearch licenses this file to you under
 * the Apache License, Version 2.0 (the "License"); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import org.opensearch.gradle.testclusters.StandaloneRestIntegTestTask
import java.util.concurrent.Callable

apply plugin: 'opensearch.build'
apply plugin: 'opensearch.java-agent'
apply plugin: 'opensearch.rest-test'
apply plugin: 'java'

apply plugin: 'opensearch.testclusters'

ext {
    licenseFile = rootProject.file('LICENSE.TXT')
    noticeFile = rootProject.file('NOTICE')
}

buildscript {
    ext {
        opensearch_version = System.getProperty("opensearch.version", "3.1.0-SNAPSHOT")
        opensearch_group = "org.opensearch"
    }
    repositories {
        mavenLocal()
        maven { url "https://aws.oss.sonatype.org/content/repositories/snapshots" }
        mavenCentral()
        maven { url "https://plugins.gradle.org/m2/" }
    }

    dependencies {
        classpath "${opensearch_group}.gradle:build-tools:${opensearch_version}"
    }
}

repositories {
    mavenLocal()
    maven { url "https://aws.oss.sonatype.org/content/repositories/snapshots" }
    mavenCentral()
    maven { url "https://plugins.gradle.org/m2/" }
}

dependencies {
    testImplementation "com.google.guava:guava:${versions.guava}"
    testImplementation "org.opensearch.test:framework:${opensearch_version}"
    testImplementation "org.apache.logging.log4j:log4j-core:${versions.log4j}"
}

loggerUsageCheck.enabled = false
testingConventions.enabled = false
validateNebulaPom.enabled = false

String previousVersion = System.getProperty("bwc.version.previous", "2.11.1.0")
String nextVersion = System.getProperty("bwc.version.next", "3.0.0.0")

String bwcVersion = previousVersion
String baseName = "customCodecsBwcCluster"
String bwcFilePath = "src/test/resources/"
String projectVersion = nextVersion

String previousOpenSearch  = extractVersion(previousVersion) + "-SNAPSHOT";
String nextOpenSearch  = extractVersion(nextVersion) + "-SNAPSHOT";

// Extracts the OpenSearch version from a plugin version string, 2.11.0.0 -> 2.11.0.
def String extractVersion(versionStr) {
    def versionMatcher =  versionStr =~ /(.+?)(\.\d+)(-(alpha|beta|rc)\d+)?$/
    versionMatcher.find()
    if (versionMatcher.group(3) != null) { /* there is a qualifier */
        return versionMatcher.group(1) + versionMatcher.group(3)
    } else {
        return versionMatcher.group(1)
    }
}

2.times {i ->
    testClusters {
        "${baseName}$i" {
            testDistribution = "ARCHIVE"
            versions = [previousOpenSearch, nextOpenSearch]
            numberOfNodes = 3
            plugin(provider(new Callable<RegularFile>() {
                @Override
                RegularFile call() throws Exception {
                    return new RegularFile() {
                        @Override
                        File getAsFile() {
                            return fileTree(bwcFilePath  + bwcVersion).getSingleFile()
                        }
                    }
                }
            }))
            nodes.each { node ->
                def plugins = node.plugins
                def firstPlugin = plugins.get(0)
                plugins.remove(0)
                plugins.add(firstPlugin)

                node.setting("network.bind_host", "0.0.0.0")
                node.setting("network.publish_host", "0.0.0.0")

            }

            setting 'path.repo', "${buildDir}/cluster/shared/repo/${baseName}"
            setting 'http.content_type.required', 'true'
        }
    }
}

List<Provider<RegularFile>> plugins = [
        provider(new Callable<RegularFile>(){
            @Override
            RegularFile call() throws Exception {
                return new RegularFile() {
                    @Override
                    File getAsFile() {
                        return fileTree(bwcFilePath  + projectVersion).getSingleFile()
                    }
                }
            }
        })
]

// Creates a test cluster with 3 nodes of the old version.
2.times {i ->
    task "${baseName}#oldVersionClusterTask$i"(type: StandaloneRestIntegTestTask) {
        useCluster testClusters."${baseName}$i"
        systemProperty 'tests.rest.bwcsuite', 'old_cluster'
        systemProperty 'tests.rest.bwcsuite_round', 'old'
        systemProperty 'tests.plugin_bwc_version', bwcVersion

        systemProperty 'tests.security.manager', 'true'
        systemProperty "https", System.getProperty("https")
        systemProperty "user", System.getProperty("user")
        systemProperty "password", System.getProperty("password")

        nonInputProperties.systemProperty('tests.rest.cluster', "${-> testClusters."${baseName}$i".allHttpSocketURI.join(",")}")
        nonInputProperties.systemProperty('tests.clustername', "${-> testClusters."${baseName}$i".getName()}")
    }
}

// Upgrades one node of the old cluster to new OpenSearch version with upgraded plugin version
// This results in a mixed cluster with 2 nodes on the old version and 1 upgraded node.
// This is also used as a one third upgraded cluster for a rolling upgrade.
task "${baseName}#mixedClusterTask"(type: StandaloneRestIntegTestTask) {
    dependsOn "${baseName}#oldVersionClusterTask0"
    useCluster testClusters."${baseName}0"
    doFirst {
        testClusters."${baseName}0".upgradeNodeAndPluginToNextVersion(plugins)
    }
    systemProperty 'tests.rest.bwcsuite', 'mixed_cluster'
    systemProperty 'tests.rest.bwcsuite_round', 'first'
    systemProperty 'tests.plugin_bwc_version', bwcVersion

    systemProperty 'tests.security.manager', 'true'
    systemProperty "https", System.getProperty("https")
    systemProperty "user", System.getProperty("user")
    systemProperty "password", System.getProperty("password")


    nonInputProperties.systemProperty('tests.rest.cluster', "${-> testClusters."${baseName}0".allHttpSocketURI.join(",")}")
    nonInputProperties.systemProperty('tests.clustername', "${-> testClusters."${baseName}0".getName()}")
}

// Upgrades the second node to new OpenSearch version with upgraded plugin version after the first node is upgraded.
// This results in a mixed cluster with 1 node on the old version and 2 upgraded nodes.
// This is used for rolling upgrade.
task "${baseName}#twoThirdsUpgradedClusterTask"(type: StandaloneRestIntegTestTask) {
    dependsOn "${baseName}#mixedClusterTask"
    useCluster testClusters."${baseName}0"
    doFirst {
        testClusters."${baseName}0".upgradeNodeAndPluginToNextVersion(plugins)
    }
    systemProperty 'tests.rest.bwcsuite', 'mixed_cluster'
    systemProperty 'tests.rest.bwcsuite_round', 'second'
    systemProperty 'tests.plugin_bwc_version', bwcVersion

    systemProperty 'tests.security.manager', 'true'
    systemProperty "https", System.getProperty("https")
    systemProperty "user", System.getProperty("user")
    systemProperty "password", System.getProperty("password")

    nonInputProperties.systemProperty('tests.rest.cluster', "${-> testClusters."${baseName}0".allHttpSocketURI.join(",")}")
    nonInputProperties.systemProperty('tests.clustername', "${-> testClusters."${baseName}0".getName()}")
}

// Upgrades the third node to new OpenSearch version with upgraded plugin version after the second node is upgraded.
// This results in a fully upgraded cluster.
// This is used for rolling upgrade.
task "${baseName}#rollingUpgradeClusterTask"(type: StandaloneRestIntegTestTask) {
    dependsOn "${baseName}#twoThirdsUpgradedClusterTask"
    useCluster testClusters."${baseName}0"
    doFirst {
        testClusters."${baseName}0".upgradeNodeAndPluginToNextVersion(plugins)
    }
    systemProperty 'tests.rest.bwcsuite', 'mixed_cluster'
    systemProperty 'tests.rest.bwcsuite_round', 'third'
    systemProperty 'tests.plugin_bwc_version', bwcVersion

    systemProperty 'tests.security.manager', 'true'
    systemProperty "https", System.getProperty("https")
    systemProperty "user", System.getProperty("user")
    systemProperty "password", System.getProperty("password")

    nonInputProperties.systemProperty('tests.rest.cluster', "${-> testClusters."${baseName}0".allHttpSocketURI.join(",")}")
    nonInputProperties.systemProperty('tests.clustername', "${-> testClusters."${baseName}0".getName()}")
}

// Upgrades all the nodes of the old cluster to new OpenSearch version with upgraded plugin version
// at the same time resulting in a fully upgraded cluster.
tasks.register("${baseName}#fullRestartClusterTask", StandaloneRestIntegTestTask) {
    dependsOn "${baseName}#oldVersionClusterTask1"
    useCluster testClusters."${baseName}1"
    doFirst {
        testClusters."${baseName}1".upgradeAllNodesAndPluginsToNextVersion(plugins)
    }
    systemProperty 'tests.rest.bwcsuite', 'upgraded_cluster'
    systemProperty 'tests.plugin_bwc_version', bwcVersion
    systemProperty 'tests.rest.bwcsuite_round', 'first'

    systemProperty 'tests.security.manager', 'true'
    systemProperty "https", System.getProperty("https")
    systemProperty "user", System.getProperty("user")
    systemProperty "password", System.getProperty("password")

    nonInputProperties.systemProperty('tests.rest.cluster', "${-> testClusters."${baseName}1".allHttpSocketURI.join(",")}")
    nonInputProperties.systemProperty('tests.clustername', "${-> testClusters."${baseName}1".getName()}")
}

// A bwc test suite which runs all the bwc tasks combined.
task bwcTestSuite(type: StandaloneRestIntegTestTask) {
    exclude '**/**' // Do not run any tests as part of this aggregate task
    dependsOn tasks.named("${baseName}#mixedClusterTask")
    dependsOn tasks.named("${baseName}#rollingUpgradeClusterTask")
    dependsOn tasks.named("${baseName}#fullRestartClusterTask")
}
