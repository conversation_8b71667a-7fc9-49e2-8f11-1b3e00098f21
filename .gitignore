
# intellij files
.idea/
*.iml
*.ipr
*.iws
build-idea/
out/

# include shared intellij config
!.idea/inspectionProfiles/Project_Default.xml
!.idea/runConfigurations/Debug_OpenSearch.xml
!.idea/vcs.xml
!.idea/icon.svg

# These files are generated in the main tree by annotation processors
benchmarks/src/main/generated/*
benchmarks/bin/*
benchmarks/build-eclipse-default/*
server/bin/*
server/build-eclipse-default/*
test/framework/build-eclipse-default/*

# eclipse files
.project
.classpath
.settings
build-eclipse/

# netbeans files
nb-configuration.xml
nbactions.xml

# gradle stuff
.gradle/
build/

# vscode stuff
.vscode/

# testing stuff
**/.local*
.vagrant/
/logs/

# osx stuff
.DS_Store

# default folders in which the create_bwc_index.py expects to find old es versions in
/backwards
/dev-tools/backwards

# needed in case docs build is run...maybe we can configure doc build to generate files under build?
html_docs

# random old stuff that we should look at the necessity of...
/tmp/
eclipse-build

# projects using testfixtures
testfixtures_shared/

# These are generated from .ci/jobs.t
.ci/jobs/

# build files generated
doc-tools/missing-doclet/bin/